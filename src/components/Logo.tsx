'use client';

import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}

export function Logo({ className, size = 'md', animated = true }: LogoProps) {
  const sizeClasses = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-4xl'
  };

  return (
    <div className={cn('flex items-center font-mono font-bold', sizeClasses[size], className)}>
      <span className="text-foreground">g</span>
      <span className={cn('text-muted-foreground', animated && 'logo-cursor')}>_</span>
    </div>
  );
}

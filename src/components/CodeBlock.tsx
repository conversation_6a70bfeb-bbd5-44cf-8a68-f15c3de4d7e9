'use client';

import { useEffect, useState } from 'react';
import { codeToHtml } from 'shiki';
import { cn } from '@/lib/utils';

interface CodeBlockProps {
  code: string;
  language: string;
  keyword?: string;
  lineNumbers?: boolean;
  className?: string;
  startLine?: number;
}

// Function to highlight keywords in already processed HTML
function highlightKeywordsInHtml(html: string, keyword: string): string {
  if (!keyword || keyword.trim() === '') return html;

  try {
    // Create a temporary DOM element to parse the HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    // Create a TreeWalker to traverse only text nodes
    const walker = document.createTreeWalker(
      doc.body,
      NodeFilter.SHOW_TEXT,
      null
    );

    const textNodes: Text[] = [];
    let node;
    
    // Collect all text nodes
    while (node = walker.nextNode()) {
      textNodes.push(node as Text);
    }

    // Process each text node
    textNodes.forEach(textNode => {
      const text = textNode.textContent || '';
      const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
      
      if (regex.test(text)) {
        // Create a document fragment to hold the new nodes
        const fragment = document.createDocumentFragment();
        let lastIndex = 0;
        let match;
        
        regex.lastIndex = 0; // Reset regex
        
        while ((match = regex.exec(text)) !== null) {
          // Add text before the match
          if (match.index > lastIndex) {
            fragment.appendChild(
              document.createTextNode(text.slice(lastIndex, match.index))
            );
          }
          
          // Add the highlighted match
          const mark = document.createElement('mark');
          mark.className = 'bg-yellow-200 dark:bg-yellow-800 px-1 rounded';
          mark.textContent = match[1];
          fragment.appendChild(mark);
          
          lastIndex = match.index + match[1].length;
        }
        
        // Add remaining text
        if (lastIndex < text.length) {
          fragment.appendChild(
            document.createTextNode(text.slice(lastIndex))
          );
        }
        
        // Replace the original text node with the fragment
        textNode.parentNode?.replaceChild(fragment, textNode);
      }
    });

    return doc.body.innerHTML;
  } catch (error) {
    console.error('Error highlighting keywords:', error);
    return html;
  }
}

export function CodeBlock({
  code,
  language,
  keyword,
  lineNumbers: _lineNumbers = true, // eslint-disable-line @typescript-eslint/no-unused-vars
  className,
  startLine: _startLine = 1 // eslint-disable-line @typescript-eslint/no-unused-vars
}: CodeBlockProps) {
  const [finalHtml, setFinalHtml] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function processCode() {
      setIsLoading(true);
      
      try {
        // Step 1: Apply syntax highlighting with Shiki
        const shikiHtml = await codeToHtml(code, {
          lang: language,
          theme: 'github-dark',
          transformers: [
            {
              pre(node) {
                // Add custom classes to the pre element
                node.properties.class = cn(
                  'overflow-x-auto p-4 rounded-lg text-sm',
                  node.properties.class
                );
              }
            }
          ]
        });

        // Step 2: Apply keyword highlighting if provided
        const highlightedHtml = keyword 
          ? highlightKeywordsInHtml(shikiHtml, keyword)
          : shikiHtml;

        setFinalHtml(highlightedHtml);
      } catch (error) {
        console.error('Error processing code:', error);
        // Fallback to plain text with basic highlighting
        const escapedCode = code
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;');
        
        const fallbackHtml = keyword
          ? escapedCode.replace(
              new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi'),
              '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>'
            )
          : escapedCode;
        
        setFinalHtml(`<pre class="overflow-x-auto p-4 rounded-lg text-sm bg-gray-900 text-gray-100"><code>${fallbackHtml}</code></pre>`);
      } finally {
        setIsLoading(false);
      }
    }

    processCode();
  }, [code, language, keyword]);

  if (isLoading) {
    return (
      <div className={cn('animate-pulse', className)}>
        <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
      </div>
    );
  }

  return (
    <div className={cn('relative group', className)}>
      {/* Copy button */}
      <button
        onClick={() => navigator.clipboard.writeText(code)}
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs"
        title="Copy code"
      >
        Copy
      </button>
      
      {/* Code content */}
      <div 
        className="code-block"
        dangerouslySetInnerHTML={{ __html: finalHtml }}
      />
    </div>
  );
}

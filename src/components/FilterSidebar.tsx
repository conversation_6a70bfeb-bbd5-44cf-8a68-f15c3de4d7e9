'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';

const LANGUAGES = [
  'JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'Go', 'Rust',
  'PHP', 'Ruby', 'Swift', 'Kotlin', 'Scala', 'Dart', 'R', 'Shell'
];

const FILE_TYPES = [
  '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.cs', '.go', '.rs',
  '.php', '.rb', '.swift', '.kt', '.scala', '.dart', '.r', '.sh', '.md', '.json'
];

const REPOSITORIES = [
  'facebook/react', 'microsoft/vscode', 'google/tensorflow', 'nodejs/node',
  'angular/angular', 'vuejs/vue', 'microsoft/TypeScript', 'python/cpython'
];

interface FilterSidebarProps {
  className?: string;
}

export function FilterSidebar({ className }: FilterSidebarProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const selectedLanguages = searchParams.getAll('lang');
  const selectedFileTypes = searchParams.getAll('ext');
  const selectedRepos = searchParams.getAll('repo');

  const updateFilter = (key: string, value: string, action: 'add' | 'remove') => {
    const newParams = new URLSearchParams(searchParams);
    
    if (action === 'add') {
      newParams.append(key, value);
    } else {
      const values = newParams.getAll(key);
      newParams.delete(key);
      values.filter(v => v !== value).forEach(v => newParams.append(key, v));
    }
    
    router.push(`/search?${newParams.toString()}`);
  };

  const clearAllFilters = () => {
    const newParams = new URLSearchParams();
    const query = searchParams.get('q');
    if (query) {
      newParams.set('q', query);
    }
    router.push(`/search?${newParams.toString()}`);
  };

  const hasActiveFilters = selectedLanguages.length > 0 || 
                          selectedFileTypes.length > 0 || 
                          selectedRepos.length > 0;

  return (
    <aside className={className}>
      <div className="space-y-6">
        {/* Clear filters */}
        {hasActiveFilters && (
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-sm">Filters</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-xs"
            >
              Clear all
            </Button>
          </div>
        )}

        <Accordion type="multiple" defaultValue={['languages', 'filetypes', 'repositories']}>
          {/* Languages */}
          <AccordionItem value="languages">
            <AccordionTrigger className="text-sm font-medium">
              Languages
              {selectedLanguages.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {selectedLanguages.length}
                </Badge>
              )}
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                {LANGUAGES.map((lang) => {
                  const isSelected = selectedLanguages.includes(lang);
                  return (
                    <button
                      key={lang}
                      onClick={() => updateFilter('lang', lang, isSelected ? 'remove' : 'add')}
                      className={`flex items-center justify-between w-full px-2 py-1 text-sm rounded hover:bg-accent transition-colors ${
                        isSelected ? 'bg-accent text-accent-foreground' : 'text-muted-foreground'
                      }`}
                    >
                      <span>{lang}</span>
                      {isSelected && <X className="h-3 w-3" />}
                    </button>
                  );
                })}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* File Types */}
          <AccordionItem value="filetypes">
            <AccordionTrigger className="text-sm font-medium">
              File Types
              {selectedFileTypes.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {selectedFileTypes.length}
                </Badge>
              )}
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                {FILE_TYPES.map((ext) => {
                  const isSelected = selectedFileTypes.includes(ext);
                  return (
                    <button
                      key={ext}
                      onClick={() => updateFilter('ext', ext, isSelected ? 'remove' : 'add')}
                      className={`flex items-center justify-between w-full px-2 py-1 text-sm rounded hover:bg-accent transition-colors font-mono ${
                        isSelected ? 'bg-accent text-accent-foreground' : 'text-muted-foreground'
                      }`}
                    >
                      <span>{ext}</span>
                      {isSelected && <X className="h-3 w-3" />}
                    </button>
                  );
                })}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Repositories */}
          <AccordionItem value="repositories">
            <AccordionTrigger className="text-sm font-medium">
              Repositories
              {selectedRepos.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {selectedRepos.length}
                </Badge>
              )}
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                {REPOSITORIES.map((repo) => {
                  const isSelected = selectedRepos.includes(repo);
                  return (
                    <button
                      key={repo}
                      onClick={() => updateFilter('repo', repo, isSelected ? 'remove' : 'add')}
                      className={`flex items-center justify-between w-full px-2 py-1 text-sm rounded hover:bg-accent transition-colors ${
                        isSelected ? 'bg-accent text-accent-foreground' : 'text-muted-foreground'
                      }`}
                    >
                      <span className="truncate">{repo}</span>
                      {isSelected && <X className="h-3 w-3" />}
                    </button>
                  );
                })}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </aside>
  );
}

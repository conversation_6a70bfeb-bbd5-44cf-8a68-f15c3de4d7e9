'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useDebounce } from 'use-debounce';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface SearchInputProps {
  className?: string;
  placeholder?: string;
  size?: 'sm' | 'md' | 'lg';
  autoFocus?: boolean;
  onSearch?: (query: string) => void;
}

export function SearchInput({ 
  className, 
  placeholder = "Search code...", 
  size = 'md',
  autoFocus = false,
  onSearch 
}: SearchInputProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [text, setText] = useState(searchParams.get('q') || '');
  const [debouncedValue] = useDebounce(text, 500);

  const sizeClasses = {
    sm: 'h-10 text-sm',
    md: 'h-12 text-base',
    lg: 'h-14 text-lg'
  };

  useEffect(() => {
    // Update local state when URL changes (e.g., browser back/forward)
    const currentQuery = searchParams.get('q') || '';
    if (currentQuery !== text) {
      setText(currentQuery);
    }
  }, [searchParams, text]);

  useEffect(() => {
    // Only update URL if debounced value is different from current URL param
    const currentQuery = searchParams.get('q') || '';
    if (debouncedValue !== currentQuery) {
      if (debouncedValue.trim()) {
        const newParams = new URLSearchParams(searchParams);
        newParams.set('q', debouncedValue.trim());
        router.push(`/search?${newParams.toString()}`);
      } else if (currentQuery) {
        // If search is cleared, go back to home
        router.push('/');
      }
      
      // Call onSearch callback if provided
      onSearch?.(debouncedValue.trim());
    }
  }, [debouncedValue, router, searchParams, onSearch]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim()) {
      const newParams = new URLSearchParams(searchParams);
      newParams.set('q', text.trim());
      router.push(`/search?${newParams.toString()}`);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={cn('relative', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder={placeholder}
          autoFocus={autoFocus}
          className={cn(
            'pl-10 pr-4 border-border focus:ring-2 focus:ring-ring focus:border-transparent',
            sizeClasses[size],
            className
          )}
        />
      </div>
    </form>
  );
}

'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, GitBranch, Star } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SearchResult {
  id: string;
  filename: string;
  path: string;
  repository: {
    name: string;
    url: string;
    stars?: number;
    language?: string;
  };
  matches: Array<{
    lineNumber: number;
    content: string;
    highlighted?: string;
  }>;
  url: string;
}

interface ResultCardProps {
  result: SearchResult;
  searchQuery?: string;
  className?: string;
}

export function ResultCard({ result, searchQuery: _searchQuery, className }: ResultCardProps) { // eslint-disable-line @typescript-eslint/no-unused-vars
  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  return (
    <Card className={cn('result-card group', className)}>
      <div className="space-y-3">
        {/* Header with file info */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span className="font-mono truncate">{result.path}</span>
              {result.repository.language && (
                <Badge variant="secondary" className="text-xs">
                  {result.repository.language}
                </Badge>
              )}
            </div>
            <h3 className="font-semibold text-foreground mt-1 group-hover:text-primary transition-colors">
              {result.filename}
            </h3>
          </div>
          
          <a
            href={result.url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-muted-foreground hover:text-foreground transition-colors p-1"
            title="Open in repository"
          >
            <ExternalLink className="h-4 w-4" />
          </a>
        </div>

        {/* Repository info */}
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <a
            href={result.repository.url}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-1 hover:text-foreground transition-colors"
          >
            <GitBranch className="h-3 w-3" />
            <span>{result.repository.name}</span>
          </a>
          
          {result.repository.stars && (
            <div className="flex items-center space-x-1">
              <Star className="h-3 w-3" />
              <span>{formatNumber(result.repository.stars)}</span>
            </div>
          )}
        </div>

        {/* Code matches */}
        <div className="space-y-1 bg-muted/30 rounded-lg p-3">
          {result.matches.slice(0, 3).map((match, index) => (
            <div key={index} className="flex text-sm font-mono">
              <span className="text-muted-foreground w-12 text-right pr-3 flex-shrink-0 select-none">
                {match.lineNumber}
              </span>
              <div className="flex-1 min-w-0">
                {match.highlighted ? (
                  <div
                    className="truncate"
                    dangerouslySetInnerHTML={{ __html: match.highlighted }}
                  />
                ) : (
                  <div className="truncate text-muted-foreground">
                    {match.content}
                  </div>
                )}
              </div>
            </div>
          ))}

          {result.matches.length > 3 && (
            <div className="text-xs text-muted-foreground pl-15 pt-1 border-t border-border/50">
              +{result.matches.length - 3} more matches
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}

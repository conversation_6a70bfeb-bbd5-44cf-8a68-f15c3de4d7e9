import { Logo } from '@/components/Logo';
import { SearchInput } from '@/components/SearchInput';

export default function Home() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-2xl mx-auto text-center space-y-8">
        {/* Logo */}
        <div className="space-y-4">
          <Logo size="lg" className="justify-center" />
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-foreground">
              Search code across millions of repositories
            </h1>
            <p className="text-lg text-muted-foreground">
              Fast, powerful code search engine for developers
            </p>
          </div>
        </div>

        {/* Search Input */}
        <div className="w-full">
          <SearchInput
            size="lg"
            placeholder="Search for code, functions, classes..."
            autoFocus
            className="w-full"
          />
        </div>

        {/* Quick examples */}
        <div className="space-y-3">
          <p className="text-sm text-muted-foreground">Try searching for:</p>
          <div className="flex flex-wrap justify-center gap-2">
            {[
              'useState',
              'async function',
              'class Component',
              'import React',
              'function main'
            ].map((example) => (
              <button
                key={example}
                className="px-3 py-1 text-xs bg-secondary text-secondary-foreground rounded-full hover:bg-secondary/80 transition-colors font-mono"
                onClick={() => {
                  const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement;
                  if (searchInput) {
                    searchInput.value = example;
                    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
                    searchInput.focus();
                  }
                }}
              >
                {example}
              </button>
            ))}
          </div>
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 text-left">
          <div className="space-y-2">
            <h3 className="font-semibold text-foreground">⚡ Lightning Fast</h3>
            <p className="text-sm text-muted-foreground">
              Search through millions of lines of code in milliseconds
            </p>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold text-foreground">🎯 Precise Results</h3>
            <p className="text-sm text-muted-foreground">
              Advanced filtering by language, repository, and file type
            </p>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold text-foreground">🔍 Smart Highlighting</h3>
            <p className="text-sm text-muted-foreground">
              Syntax highlighting with intelligent keyword matching
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
